package thirdparty

import (
	"bufio"
	"fmt"
	"os"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model/thirdparty"
	"patch-central-repo/repository"
	"patch-central-repo/rest"
	service2 "patch-central-repo/service"
	"path/filepath"
	"strings"
	"time"
)

type ThirdPartyPatchPoolingServiceRegistry interface {
	Name() string
	ExecuteSync()
}

var registry = make(map[string]ThirdPartyPatchPoolingServiceRegistry)

func RegisterCollector(c ThirdPartyPatchPoolingServiceRegistry) {
	registry[c.Name()] = c
}

type ThirdPartyPackageService struct {
	Repository *repository.ThirdPartyPackageRepository
}

func NewThirdPartyPackageService() *ThirdPartyPackageService {
	return &ThirdPartyPackageService{
		Repository: repository.NewThirdPartyPackageRepository(),
	}
}

// Convert REST to Model
func (service *ThirdPartyPackageService) convertToModel(pkgRest thirdparty.ThirdPartyPackageRest) (*thirdparty.ThirdPartyPackage, error) {
	var osType common.OsType
	osType, _ = osType.ToOsType(pkgRest.Os)

	var osArch common.OsArchitecture
	osArch, _ = osArch.ToOsArch(pkgRest.Arch)

	var app common.ThirdPartyApplication
	app = app.ToThirdPartyApplication(pkgRest.Application)

	return &thirdparty.ThirdPartyPackage{
		BaseEntityModel:  service2.ConvertToBaseEntityModel(pkgRest.BaseEntityRest),
		Description:      pkgRest.Description,
		Version:          pkgRest.Version,
		Os:               osType,
		Arch:             osArch,
		LanguageCode:     pkgRest.LanguageCode,
		PkgFileData:      pkgRest.PkgFileData,
		LatestPackageUrl: pkgRest.LatestPackageUrl,
		Publisher:        pkgRest.Publisher,
		SupportUrl:       pkgRest.SupportUrl,
		ReleaseNote:      pkgRest.ReleaseNote,
		ReleaseDate:      pkgRest.ReleaseDate,
		Application:      app,
		CveDetails:       pkgRest.CveDetails,
		Uuid:             pkgRest.Uuid,
		OsVersion:        pkgRest.OsVersion,
		ProductCode:      pkgRest.ProductCode,
		InstallCommand:   pkgRest.InstallCommand,
		UnInstallCommand: pkgRest.UnInstallCommand,
	}, nil
}

// Convert Model to REST
func (service *ThirdPartyPackageService) convertToRest(pkg *thirdparty.ThirdPartyPackage) thirdparty.ThirdPartyPackageRest {
	return thirdparty.ThirdPartyPackageRest{
		BaseEntityRest:   service2.ConvertToBaseEntityRest(pkg.BaseEntityModel),
		Description:      pkg.Description,
		Version:          pkg.Version,
		Os:               pkg.Os.String(),
		Arch:             pkg.Arch.String(),
		LanguageCode:     pkg.LanguageCode,
		PkgFileData:      pkg.PkgFileData,
		LatestPackageUrl: pkg.LatestPackageUrl,
		Publisher:        pkg.Publisher,
		SupportUrl:       pkg.SupportUrl,
		ReleaseNote:      pkg.ReleaseNote,
		ReleaseDate:      pkg.ReleaseDate,
		Application:      pkg.Application.String(),
		CveDetails:       pkg.CveDetails,
		Uuid:             pkg.Uuid,
		OsVersion:        pkg.OsVersion,
		ProductCode:      pkg.ProductCode,
		InstallCommand:   pkg.InstallCommand,
		UnInstallCommand: pkg.UnInstallCommand,
	}
}

func (service ThirdPartyPackageService) Create(rest thirdparty.ThirdPartyPackageRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to create package"))

	pkg, _ := service.convertToModel(rest)
	pkg.CreatedTime = time.Now().UnixMilli()
	pkg.UpdatedTime = time.Now().UnixMilli()
	id, err := service.Repository.Create(pkg)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating user ,Error : %s ", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}
	return id, common.CustomError{}
}

func (service ThirdPartyPackageService) SyncThirdPartyPatchRepo() {
	logger.ServiceLogger.Debug("[SyncThirdPartyPatchRepo] : syncing third party patch repositories....")
	for name, collector := range registry {
		fmt.Println("Running Patch pooling for:", name)
		collector.ExecuteSync()
	}
	logger.ServiceLogger.Debug("[SyncThirdPartyPatchRepo] : third party patch repositories syncing completed....")
}

func DeleteXmlForWindows(uuid string, application common.ThirdPartyApplication) {
	filePath := filepath.Join(common.ThirdPartyXMLDirectoryPath(), strings.ToLower(application.String()), uuid+".xml")
	_, err := os.Stat(filePath)
	if !os.IsNotExist(err) {
		err = os.Remove(filePath)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}
}

func GenerateXmlForWindows(data map[string]interface{}, version string, arch common.OsArchitecture, uuid string, application common.ThirdPartyApplication) {
	templateName := data["templateFileNameMap"].(map[string]interface{})[strings.ToLower(arch.String())]
	if templateName != nil {
		fileData, err := os.ReadFile(filepath.Join(common.ThirdPartyTemplateXMLDirectoryPath(), templateName.(string)))
		if err != nil {
			logger.ServiceLogger.Error("ThirdPartyPackageService.GenerateXmlForWindows error : ", err)
		}
		fileContent := string(fileData)
		if strings.Contains(fileContent, "{#version#}") {
			fileContent = strings.ReplaceAll(fileContent, "{#version#}", version)
		}
		if strings.Contains(fileContent, "{#uuid#}") {
			fileContent = strings.ReplaceAll(fileContent, "{#uuid#}", uuid)
		}
		if strings.Contains(fileContent, "{#osVersionConditionXmlString#}") {
			fileContent = strings.ReplaceAll(fileContent, "{#osVersionConditionXmlString#}", data["osVersionConditionXmlString"].(string))
		}

		if strings.Contains(fileContent, "{#osArch#}") {
			if arch == common.X86 {
				fileContent = strings.ReplaceAll(fileContent, "{#osArch#}", "0")
			} else if arch == common.X64 {
				fileContent = strings.ReplaceAll(fileContent, "{#osArch#}", "9")
			}

		}

		if strings.Contains(fileContent, "{#productCode#}") {
			fileContent = strings.ReplaceAll(fileContent, "{#productCode#}", data["productCodeMap"].(map[string]interface{})[strings.ToLower(arch.String())].(string))
		}

		filePath := filepath.Join(common.ThirdPartyXMLDirectoryPath(), strings.ToLower(application.String()))
		_, err = os.Stat(filePath)
		if os.IsNotExist(err) {
			err := os.MkdirAll(filePath, 0755)
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
		}

		filePath = filepath.Join(filePath, uuid+".xml")
		err = os.Remove(filePath)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
		xmlFile, err := os.Create(filePath)
		if err != nil {
			logger.ServiceLogger.Error("Error creating xml file ", filePath, " for uuid ", uuid)
		}
		defer func(xmlFile *os.File) {
			err := xmlFile.Close()
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
		}(xmlFile)

		_, err = xmlFile.Write([]byte(fileContent))
		if err != nil {
			logger.ServiceLogger.Error("Error writing xml file ", filePath, " for uuid ", uuid)
		}
	}
}

func (service ThirdPartyPackageService) GetAllPackages(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareQueryFromSearchFilter(filter, common.ThirdPartyPackage.String(), true)
	var responsePage rest.ListResponseRest
	var packagePageList []thirdparty.ThirdPartyPackage
	var err error
	count := service.Repository.Count(countQuery)
	if count > 0 {
		searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.ThirdPartyPackage.String(), false)
		packagePageList, err = service.Repository.GetAllPackage(searchQuery)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(packagePageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service *ThirdPartyPackageService) convertListToRest(packList []thirdparty.ThirdPartyPackage) []thirdparty.ThirdPartyPackageRest {
	var packRestList []thirdparty.ThirdPartyPackageRest
	if len(packList) != 0 {
		for _, pack := range packList {
			packRest := service.convertToRest(&pack)
			packRestList = append(packRestList, packRest)
		}
	}
	return packRestList
}

func (service *ThirdPartyPackageService) CreateRequiredFiles(application common.ThirdPartyApplication) {
	xmlStoreFolder := filepath.Join(common.ThirdPartyXMLDirectoryPath(), strings.ToLower(application.String()))
	createAllPatchTXTFile(xmlStoreFolder, strings.ToLower(application.String()))
	create7ZFileOfApplication(strings.ToLower(application.String()))
}

func create7ZFileOfApplication(applicationFolderName string) {
	xmlStoreFolder := filepath.Join(common.ThirdPartyXMLDirectoryPath(), strings.ToLower(applicationFolderName))
	expectedFileName := strings.ReplaceAll(xmlStoreFolder, " ", "") + ".7z"
	_, err := os.Stat(expectedFileName)
	if !os.IsNotExist(err) {
		err = os.Remove(expectedFileName)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}
	common.CreateZipFile(xmlStoreFolder, filepath.Join(xmlStoreFolder, "*"))
}

func createAllPatchTXTFile(xmlStoreFolder, applicationFolderName string) {
	// Read all files from the XML store folder
	entries, err := os.ReadDir(xmlStoreFolder)
	if err != nil {
		logger.ServiceLogger.Error("Error reading directory ", xmlStoreFolder, ": ", err)
		return
	}

	// Create a set to store unique filenames (without extensions)
	fileNames := make(map[string]bool)

	// Filter files and extract filenames without extensions
	for _, entry := range entries {
		if !entry.IsDir() {
			fileName := entry.Name()
			// Skip files that contain "allpatchlist" in their name
			if !strings.Contains(fileName, "allpatchlist") {
				// Remove file extension
				nameWithoutExt := fileName
				if lastDotIndex := strings.LastIndex(fileName, "."); lastDotIndex != -1 {
					nameWithoutExt = fileName[:lastDotIndex]
				}
				fileNames[nameWithoutExt] = true
			}
		}
	}

	// Only proceed if we have files to write
	if len(fileNames) > 0 {
		allPatchListPath := filepath.Join(xmlStoreFolder, "allpatchlist.txt")

		// Remove existing file if it exists
		if _, err := os.Stat(allPatchListPath); err == nil {
			if err := os.Remove(allPatchListPath); err != nil {
				logger.ServiceLogger.Error("Error removing existing allpatchlist.txt: ", err)
			}
		}

		// Create the new file
		file, err := os.Create(allPatchListPath)
		if err != nil {
			logger.ServiceLogger.Error("Error creating allpatchlist.txt file: ", err)
			return
		}
		defer func() {
			if err := file.Close(); err != nil {
				logger.ServiceLogger.Error("Error closing allpatchlist.txt file: ", err)
			}
		}()

		// Create a buffered writer for better performance
		writer := bufio.NewWriter(file)
		defer func() {
			if err := writer.Flush(); err != nil {
				logger.ServiceLogger.Error("Error flushing writer: ", err)
			}
		}()

		// Write each filename in the format: filename,applicationFolderName,""
		for fileName := range fileNames {
			line := fmt.Sprintf("%s,%s,0,\"\"\n", fileName, applicationFolderName)
			if _, err := writer.WriteString(line); err != nil {
				logger.ServiceLogger.Error("Error writing to allpatchlist.txt: ", err)
				// Remove the file if there's an error writing to it
				if removeErr := os.Remove(allPatchListPath); removeErr != nil {
					logger.ServiceLogger.Error("Error removing corrupted allpatchlist.txt: ", removeErr)
				}
				return
			}
		}
	}
}

func GenerateInstallUninstallCommands(application common.ThirdPartyApplication) (string, string) {

	var installCommand, uninstallCommand string

	switch application {
	case common.FIREFOX:
		installCommand = "/S"
		uninstallCommand = "/S"

	case common.VLC:
		installCommand = "/S"
		uninstallCommand = "/S"

	case common.CHROME:
		installCommand = "/silent /install"
		uninstallCommand = "/silent /uninstall"

	case common.NOTEPAD_PLUS_PLUS:
		installCommand = "/S"
		uninstallCommand = "/S"
	case common.WINRAR:
		installCommand = "/S"
		uninstallCommand = "/S"
	case common.VISUAL_STUDIO_CODE:
		installCommand = "/S"
		uninstallCommand = "/S"
	case common.POSTMAN:
		installCommand = "/S"
		uninstallCommand = "/S"
	case common.DBEAVER:
		installCommand = "/S"
		uninstallCommand = "/S"
	case common.ANYDESK:
		installCommand = "--install --silent"
		uninstallCommand = "--uninstall --silent"
	case common.TEAMVIEWER:
		installCommand = "/S"
		uninstallCommand = "/S"
	case common.ZOOM:
		installCommand = "/silent"
		uninstallCommand = "/uninstall /quiet"
	default:
		installCommand = ""
		uninstallCommand = ""
	}

	return installCommand, uninstallCommand
}
